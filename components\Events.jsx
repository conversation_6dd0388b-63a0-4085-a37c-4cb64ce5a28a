import React from "react";
import Container from "@mui/material/Container";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Image from "next/image";
import Button from "@mui/material/Button";

{
  /* Swiss National Day Special Event Section */
}
function Events() {
  return (
    <Box
      component="section"
      sx={{
        position: "relative",
        minHeight: { xs: "600px", md: "700px" },
        background: `
            radial-gradient(circle at 20% 80%, rgba(103, 247, 86, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(74, 29, 31, 0.2) 0%, transparent 50%),
            linear-gradient(135deg, #09090B 0%, #0f0f0f 25%, #1a1a1a 50%, #0f0f0f 75%, #09090B 100%)
          `,
        overflow: "hidden",
        display: "flex",
        alignItems: "center",
        py: { xs: 6, md: 8 },
      }}
    >
      {/* Modern Geometric Background */}
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          background: `
            linear-gradient(45deg, transparent 30%, rgba(103, 247, 86, 0.03) 30%, rgba(103, 247, 86, 0.03) 32%, transparent 32%),
            linear-gradient(-45deg, transparent 30%, rgba(103, 247, 86, 0.03) 30%, rgba(103, 247, 86, 0.03) 32%, transparent 32%)
          `,
          backgroundSize: "60px 60px",
          zIndex: 1,
        }}
      />

      {/* Floating orbs */}
      <Box
        sx={{
          position: "absolute",
          top: "20%",
          left: "10%",
          width: "200px",
          height: "200px",
          background:
            "radial-gradient(circle, rgba(103, 247, 86, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          filter: "blur(40px)",
          animation: "float 6s ease-in-out infinite",
        }}
      />

      <Box
        sx={{
          position: "absolute",
          bottom: "20%",
          right: "15%",
          width: "150px",
          height: "150px",
          background:
            "radial-gradient(circle, rgba(74, 29, 31, 0.15) 0%, transparent 70%)",
          borderRadius: "50%",
          filter: "blur(30px)",
          animation: "float 8s ease-in-out infinite reverse",
        }}
      />

      <Container sx={{ position: "relative", zIndex: 2, maxWidth: "1400px" }}>
        <Grid container spacing={{ xs: 4, md: 8 }} alignItems="center">
          <Grid item xs={12} md={6}>
            <Box
              sx={{
                textAlign: { xs: "center", md: "left" },
                color: "white",
                pl: { xs: 0, md: 4 },
              }}
            >
              <Box
                sx={{
                  display: "inline-flex",
                  alignItems: "center",
                  gap: 1,
                  backgroundColor: "rgba(103, 247, 86, 0.1)",
                  border: "1px solid rgba(103, 247, 86, 0.3)",
                  borderRadius: "50px",
                  px: 3,
                  py: 1,
                  mb: 4,
                  backdropFilter: "blur(10px)",
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: "#67f756",
                    fontWeight: "600",
                    fontSize: { xs: "0.9rem", md: "1rem" },
                    letterSpacing: "0.5px",
                  }}
                >
                  Swiss National Day
                </Typography>
              </Box>

              <Typography
                variant="h1"
                component="h2"
                sx={{
                  fontWeight: "800",
                  mb: 3,
                  fontSize: { xs: "2.5rem", md: "4rem", lg: "4.5rem" },
                  lineHeight: { xs: 1.1, md: 1.05 },
                  color: "white",
                  background:
                    "linear-gradient(135deg, #ffffff 0%, #67f756 100%)",
                  backgroundClip: "text",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  letterSpacing: "-0.02em",
                }}
              >
                SPEZIAL
                <br />
                EVENT
              </Typography>

              <Box>
                <Typography
                  variant="h4"
                  sx={{
                    fontSize: { xs: "1.3rem", md: "1.8rem" },
                    fontWeight: "600",
                    color: "#67f756",
                    letterSpacing: "0.5px",
                    display: "inline-flex",
                    alignItems: "center",
                    backgroundColor: "rgba(255, 255, 255, 0.05)",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                    borderRadius: "12px",
                    px: 4,
                    py: 2,
                    mb: 3,
                    backdropFilter: "blur(10px)",
                  }}
                >
                  Thursday, July 31TH
                </Typography>
              </Box>

              <Typography
                variant="body1"
                sx={{
                  fontSize: { xs: "1.1rem", md: "1.25rem" },
                  lineHeight: 1.7,
                  maxWidth: "550px",
                  mx: { xs: "auto", md: 0 },
                  color: "#C6C6C6",
                  fontWeight: "400",
                }}
              >
                Feiern Sie mit uns den Schweizer Nationalfeiertag auf unserer
                exklusiven Rooftop-Terrasse! Ein unvergesslicher Abend mit
                atemberaubendem Ausblick, erstklassiger Shisha und patriotischer
                Atmosphäre erwartet Sie.
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  flexDirection: { xs: "column", sm: "row" },
                  gap: 3,
                  mt: 3,
                  justifyContent: { xs: "center", md: "flex-start" },
                  alignItems: { xs: "center", sm: "flex-start" },
                }}
              >
                <Button
                  variant="contained"
                  size="large"
                  sx={{
                    backgroundColor: "#67f756",
                    color: "#09090B",
                    fontWeight: "700",
                    fontSize: { xs: "1rem", md: "1.1rem" },
                    px: { xs: 2, md: 4 },
                    py: { xs: 2, md: 2.5 },
                    borderRadius: "50px",
                    textTransform: "none",
                    letterSpacing: "0.5px",
                    boxShadow: "0 4px 20px rgba(103, 247, 86, 0.3)",
                    border: "2px solid transparent",
                    minWidth: { xs: "280px", sm: "auto" },
                    "&:hover": {
                      backgroundColor: "#a4fa9a",
                      transform: "translateY(-3px)",
                      boxShadow: "0 8px 30px rgba(103, 247, 86, 0.5)",
                      border: "2px solid #67f756",
                    },
                    transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                  }}
                >
                  Jetzt Reservieren
                </Button>

                <Button
                  variant="outlined"
                  size="large"
                  sx={{
                    borderColor: "rgba(255, 255, 255, 0.3)",
                    color: "white",
                    fontWeight: "600",
                    fontSize: { xs: "1rem", md: "1.1rem" },
                    px: { xs: 6, md: 8 },
                    py: { xs: 2, md: 2.5 },
                    borderRadius: "50px",
                    textTransform: "none",
                    letterSpacing: "0.5px",
                    borderWidth: "2px",
                    backgroundColor: "rgba(255, 255, 255, 0.05)",
                    backdropFilter: "blur(10px)",
                    minWidth: { xs: "280px", sm: "auto" },
                    "&:hover": {
                      backgroundColor: "rgba(103, 247, 86, 0.1)",
                      borderColor: "#67f756",
                      color: "#67f756",
                      transform: "translateY(-3px)",
                      boxShadow: "0 8px 25px rgba(103, 247, 86, 0.2)",
                    },
                    transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                  }}
                >
                  Mehr Infos
                </Button>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box
              sx={{
                position: "relative",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                pr: { xs: 0, md: 4 },
              }}
            >
              <Box
                sx={{
                  position: "relative",
                  borderRadius: "24px",
                  overflow: "hidden",
                  background:
                    "linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)",
                  padding: "8px",
                  backdropFilter: "blur(20px)",
                  border: "1px solid rgba(255,255,255,0.1)",
                  boxShadow: `
                    0 25px 50px rgba(0,0,0,0.4),
                    0 0 0 1px rgba(103, 247, 86, 0.1),
                    inset 0 1px 0 rgba(255,255,255,0.1)
                  `,
                  transition: "all 0.5s cubic-bezier(0.4, 0, 0.2, 1)",
                  "&:hover": {
                    transform: "translateY(-10px) scale(1.02)",
                    boxShadow: `
                      0 35px 70px rgba(0,0,0,0.5),
                      0 0 0 1px rgba(103, 247, 86, 0.3),
                      inset 0 1px 0 rgba(255,255,255,0.2)
                    `,
                  },
                }}
              >
                <Box
                  sx={{
                    borderRadius: "16px",
                    overflow: "hidden",
                    position: "relative",
                  }}
                >
                  <Image
                    src="https://images.ctfassets.net/87jhdyn6f199/2MnOoVazdjm8uQNcaEOoD1/2dd1c3d2dd77cc70ae6bcd504bed48f8/poster.jpg"
                    alt="Swiss National Day Special Event at Vibes Rooftop"
                    width={400}
                    height={600}
                    style={{
                      width: "100%",
                      height: "auto",
                      maxWidth: "420px",
                      display: "block",
                    }}
                  />

                  {/* Modern overlay effect */}
                  <Box
                    sx={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      width: "100%",
                      height: "100%",
                      background: `
                        linear-gradient(135deg,
                          rgba(103,247,86,0.05) 0%,
                          transparent 30%,
                          transparent 70%,
                          rgba(74,29,31,0.05) 100%
                        )
                      `,
                      pointerEvents: "none",
                    }}
                  />
                </Box>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Container>

      {/* Modern floating elements */}
      <Box
        sx={{
          position: "absolute",
          top: "15%",
          left: "8%",
          width: "12px",
          height: "12px",
          backgroundColor: "#67f756",
          borderRadius: "50%",
          opacity: 0.6,
          animation: "float 4s ease-in-out infinite",
          boxShadow: "0 0 20px rgba(103, 247, 86, 0.5)",
        }}
      />

      <Box
        sx={{
          position: "absolute",
          top: "25%",
          left: "12%",
          width: "8px",
          height: "8px",
          backgroundColor: "#67f756",
          borderRadius: "50%",
          opacity: 0.4,
          animation: "float 5s ease-in-out infinite reverse",
        }}
      />

      <Box
        sx={{
          position: "absolute",
          bottom: "20%",
          right: "12%",
          width: "16px",
          height: "16px",
          backgroundColor: "#67f756",
          borderRadius: "50%",
          opacity: 0.5,
          animation: "float 6s ease-in-out infinite",
          boxShadow: "0 0 15px rgba(103, 247, 86, 0.4)",
        }}
      />
    </Box>
  );
}

export default Events;
