"use client";

import React, { useEffect, useRef } from 'react';
import { Box, Container, Typography, Paper, Fade } from '@mui/material';
import { BsStar, BsStarFill } from 'react-icons/bs';

const ReviewWidget = () => {
  const widgetRef = useRef(null);

  useEffect(() => {
    // Load the review widget script
    const script = document.createElement('script');
    script.src = 'https://widget.review-widget.net/js/widget.js';
    script.async = true;
    document.head.appendChild(script);

    return () => {
      // Cleanup script on unmount
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, []);

  return (
    <Box
      component="section"
      sx={{
        py: { xs: 6, md: 8 },
        position: 'relative',
        overflow: 'hidden',
        background: `
          radial-gradient(circle at 30% 70%, rgba(103, 247, 86, 0.08) 0%, transparent 50%),
          radial-gradient(circle at 70% 30%, rgba(74, 29, 31, 0.12) 0%, transparent 50%),
          linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, transparent 50%)
        `,
      }}
    >
      {/* Modern geometric background pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: `
            linear-gradient(45deg, transparent 48%, rgba(103, 247, 86, 0.03) 49%, rgba(103, 247, 86, 0.03) 51%, transparent 52%),
            linear-gradient(-45deg, transparent 48%, rgba(103, 247, 86, 0.03) 49%, rgba(103, 247, 86, 0.03) 51%, transparent 52%)
          `,
          backgroundSize: '80px 80px',
          opacity: 0.5,
          zIndex: 0,
        }}
      />

      <Container sx={{ position: 'relative', zIndex: 1 }}>
        {/* Section Header */}
        <Box sx={{ textAlign: 'center', mb: { xs: 4, md: 6 } }}>
          <Fade in timeout={800}>
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                {[...Array(5)].map((_, index) => (
                  <BsStarFill
                    key={index}
                    style={{
                      color: '#67f756',
                      fontSize: '24px',
                      margin: '0 2px',
                      filter: 'drop-shadow(0 0 8px rgba(103, 247, 86, 0.4))',
                    }}
                  />
                ))}
              </Box>
              
              <Typography
                variant="h2"
                component="h2"
                sx={{
                  fontSize: { xs: 28, md: 38 },
                  fontWeight: 700,
                  mb: 2,
                  background: 'linear-gradient(135deg, #ffffff 0%, #67f756 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  textAlign: 'center',
                }}
              >
                Was unsere Gäste sagen
              </Typography>
              
              <Typography
                variant="body1"
                sx={{
                  color: 'text.secondary',
                  fontSize: { xs: 16, md: 18 },
                  maxWidth: '600px',
                  mx: 'auto',
                  lineHeight: 1.6,
                }}
              >
                Erleben Sie, warum Vibes Rooftop der perfekte Ort für unvergessliche Momente ist
              </Typography>
            </Box>
          </Fade>
        </Box>

        {/* Modern Review Widget Container */}
        <Fade in timeout={1200}>
          <Paper
            elevation={0}
            sx={{
              p: { xs: 3, md: 4 },
              background: `
                linear-gradient(135deg, 
                  rgba(255, 255, 255, 0.08) 0%, 
                  rgba(255, 255, 255, 0.04) 50%, 
                  rgba(103, 247, 86, 0.06) 100%
                )
              `,
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '24px',
              position: 'relative',
              overflow: 'hidden',
              transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: '0 20px 40px rgba(103, 247, 86, 0.15)',
                border: '1px solid rgba(103, 247, 86, 0.3)',
              },
            }}
          >
            {/* Subtle glow effect */}
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                background: 'radial-gradient(circle at 50% 0%, rgba(103, 247, 86, 0.1) 0%, transparent 70%)',
                pointerEvents: 'none',
                zIndex: 0,
              }}
            />

            {/* Review Widget */}
            <Box
              ref={widgetRef}
              sx={{
                position: 'relative',
                zIndex: 1,
                '& .review-widget_net': {
                  width: '100%',
                  minHeight: '400px',
                },
                // Custom styling for the widget content
                '& iframe': {
                  borderRadius: '16px',
                  border: 'none',
                },
              }}
            >
              <div
                className="review-widget_net"
                data-uuid="9d33039b-9d4f-4a17-be3f-1a4b0a4950dc"
                data-template="10"
                data-lang="de"
                data-theme="dark"
              />
            </Box>
          </Paper>
        </Fade>

        {/* Call to Action */}
        <Fade in timeout={1600}>
          <Box sx={{ textAlign: 'center', mt: { xs: 4, md: 6 } }}>
            <Typography
              variant="body2"
              sx={{
                color: 'text.secondary',
                fontSize: 14,
                opacity: 0.8,
              }}
            >
              Werden Sie Teil unserer zufriedenen Gäste
            </Typography>
          </Box>
        </Fade>
      </Container>
    </Box>
  );
};

export default ReviewWidget;
